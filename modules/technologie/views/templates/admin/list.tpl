{*
* Admin šablona pro seznam technologií
* Kompatibilní s PrestaShop 8.2.0
*}

<div class="technologie-admin">
    <div class="panel">
        <div class="panel-heading">
            <i class="icon-cogs"></i>
            {l s='Seznam technologií' mod='technologie'}
            <span class="badge badge-info">{$technologie_count|default:0}</span>
        </div>

        <div class="panel-body">
            {if isset($errors) && $errors}
                <div class="alert alert-danger">
                    <ul class="list-unstyled">
                        {foreach $errors as $error}
                            <li>{$error}</li>
                        {/foreach}
                    </ul>
                </div>
            {/if}

            {if isset($confirmations) && $confirmations}
                <div class="alert alert-success">
                    <ul class="list-unstyled">
                        {foreach $confirmations as $confirmation}
                            <li>{$confirmation}</li>
                        {/foreach}
                    </ul>
                </div>
            {/if}

            {* Toolbar s akcemi *}
            <div class="toolbar-container mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <a href="{$add_url}" class="btn btn-primary">
                            <i class="icon-plus"></i>
                            {l s='Přidat technologii' mod='technologie'}
                        </a>
                    </div>
                    <div class="col-md-6 text-right">
                        <div class="btn-group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                {l s='Hromadné akce' mod='technologie'}
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu" role="menu">
                                <li><a href="#" id="bulk-activate">{l s='Aktivovat vybrané' mod='technologie'}</a></li>
                                <li><a href="#" id="bulk-deactivate">{l s='Deaktivovat vybrané' mod='technologie'}</a></li>
                                <li class="divider"></li>
                                <li><a href="#" id="bulk-delete" class="text-danger">{l s='Smazat vybrané' mod='technologie'}</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            {* Tabulka s technologiemi *}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th width="60" class="text-center">
                                <input type="checkbox" id="select-all" title="{l s='Vybrat vše' mod='technologie'}">
                            </th>
                            <th width="60" class="text-center">{l s='Pozice' mod='technologie'}</th>
                            <th width="80" class="text-center">{l s='Obrázek' mod='technologie'}</th>
                            <th width="200">{l s='Název' mod='technologie'}</th>
                            <th>{l s='Popis' mod='technologie'}</th>
                            <th width="80" class="text-center">{l s='Stav' mod='technologie'}</th>
                            <th width="120" class="text-center">{l s='Akce' mod='technologie'}</th>
                        </tr>
                    </thead>
                    <tbody id="sortable-technologie">
                        {if isset($technologie_list) && $technologie_list}
                            {foreach $technologie_list as $tech}
                                <tr class="technologie-row" data-id="{$tech.id}">
                                    <td class="text-center">
                                        <div class="checkbox-drag-container">
                                            <input type="checkbox" class="row-selector" value="{$tech.id}" name="selected_technologie[]">
                                            <span class="drag-handle" title="{l s='Přetáhnout pro změnu pořadí' mod='technologie'}">
                                                <i class="icon-move"></i>
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="position-badge badge badge-info">{$tech.position}</span>
                                    </td>
                                    <td>
                                        {if $tech.image}
                                            <img src="{$upload_dir}{$tech.image}" 
                                                 alt="{$tech.name|escape:'html':'UTF-8'}" 
                                                 class="technologie-thumb"
                                                 title="{$tech.name|escape:'html':'UTF-8'}">
                                        {else}
                                            <div class="no-image-placeholder">
                                                <i class="icon-picture-o"></i>
                                            </div>
                                        {/if}
                                    </td>
                                    <td>
                                        <strong>{$tech.name|escape:'html':'UTF-8'}</strong>
                                    </td>
                                    <td>
                                        <div class="technologie-description" title="{$tech.description|escape:'html':'UTF-8'}">
                                            {$tech.description|truncate:200:'...'|escape:'html':'UTF-8'}
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        {if $tech.active}
                                            <span class="badge badge-success status-toggle"
                                                  data-id="{$tech.id}"
                                                  data-status="1"
                                                  title="{l s='Klikněte pro deaktivaci' mod='technologie'}"
                                                  style="cursor: pointer;">
                                                {l s='Aktivní' mod='technologie'}
                                            </span>
                                        {else}
                                            <span class="badge badge-danger status-toggle"
                                                  data-id="{$tech.id}"
                                                  data-status="0"
                                                  title="{l s='Klikněte pro aktivaci' mod='technologie'}"
                                                  style="cursor: pointer;">
                                                {l s='Neaktivní' mod='technologie'}
                                            </span>
                                        {/if}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{$tech.edit_url}" class="btn btn-default btn-sm" title="{l s='Upravit' mod='technologie'}">
                                                <i class="icon-edit"></i>
                                            </a>
                                            <a href="{$tech.delete_url}" class="btn btn-danger btn-sm delete-tech" 
                                               title="{l s='Smazat' mod='technologie'}"
                                               data-confirm="{l s='Opravdu chcete smazat tuto technologii?' mod='technologie'}">
                                                <i class="icon-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {/foreach}
                        {else}
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <p class="mt-3 mb-3">
                                        <i class="icon-info-circle"></i>
                                        {l s='Zatím nejsou vytvořeny žádné technologie.' mod='technologie'}
                                    </p>
                                    <a href="{$add_url}" class="btn btn-primary">
                                        <i class="icon-plus"></i>
                                        {l s='Přidat první technologii' mod='technologie'}
                                    </a>
                                </td>
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>

            {* Informace o drag & drop *}
            {if isset($technologie_list) && $technologie_list && count($technologie_list) > 1}
                <div class="alert alert-info mt-3">
                    <i class="icon-info-circle"></i>
                    {l s='Tip: Pořadí technologií můžete změnit přetažením řádků pomocí myši.' mod='technologie'}
                </div>
            {/if}
        </div>
    </div>
</div>

{* Načtení CSS a JS pro admin *}
<link rel="stylesheet" type="text/css" href="{$module_dir}views/css/admin.css">
<script type="text/javascript">
    window.token = '{$smarty.get.token|escape:'javascript':'UTF-8'}';
    window.adminController = 'AdminTechnologie';
</script>
<script type="text/javascript" src="{$module_dir}views/js/admin.js"></script>
